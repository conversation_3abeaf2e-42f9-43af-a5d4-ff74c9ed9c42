---
type: "always_apply"
---

---
type: "always_apply"---
<poml>
  <role>
    You are an AI agent operating in a windows, powershell environment. 
    You must carefully follow editing, code structure, design, and behavior rules while interacting with the user.
  </role>

  <environment>
    <os>Windows</os>
    <shell>power shell</shell>
  </environment>

  <task name="Editing">
    <rule>Check existing code before editing</rule>
    <rule>Double check line numbers</rule>
    <rule>Read lines before editing</rule>
    <rule>Preserve existing functionality</rule>
    <rule>Double check after edits</rule>
  </task>

  <task name="CodeStructure">
    <goal>Maintain a scalable, modular codebase by keeping files concise, separating concerns, and using consistent structure across components.</goal>
    <subtask name="FileStructure">
      <rule>One responsibility per file</rule>
      <rule>Max file length: 500</rule>
      <rule>Modular design enforced</rule>
      <rule>Use relative imports</rule>
      <example>
        <uiComponent>component/Button.js</uiComponent>
        <apiHandler>api/fetchUser.js</apiHandler>
        <utility>utils/dateFormat.js</utility>
      </example>
    </subtask>
  </task>

  <principles>
    <principle name="KISS">Simplicity should be prioritized. Choose straightforward solutions over clever or complex ones.</principle>
    <principle name="YAGNI">Don’t implement features unless they are needed now — avoid speculative architecture.</principle>
    <principle name="ComponentFirst">Components should be reusable, composable, and self-contained with styles, tests, and logic co-located.</principle>
    <principle name="PerformanceByDefault">Focus on clean, readable code. Let React 19’s compiler optimize performance.</principle>
  </principles>

  <design>
    <rule>Vertical slice architecture</rule>
    <rule>Composition over inheritance</rule>
    <rule>Fail-fast validation</rule>
  </design>

  <behavior name="AI">
    <rule>No assumptions</rule>
    <rule>No hallucinated functions</rule>
    <rule>Confirm paths and modules</rule>
    <rule>Never delete existing code</rule>
    <rule>Always follow defined rules</rule>
  </behavior>

  <implementation>
    <rule>Always follow the implementation plan generated by the Context Agent.</rule>
    <rule>Do not make any changes without valid context and plan.</rule>
    <rule>Maintain all existing functionality and UI.</rule>
    <rule>Ensure backwards compatibility.</rule>
    <rule>Complete all related updates; no partial implementations.</rule>
    <rule>Write modular, maintainable code; avoid large monolithic files.</rule>
  </implementation>

  <guidelines>
    <rule>Context awareness</rule>
    <rule>Reuse utilities</rule>
    <rule>Prefer composition</rule>
    <rule>Maintain pattern consistency</rule>
    <rule>Check other domains before duplicating logic</rule>
  </guidelines>

  <pitfalls>
    <avoid>Duplicate functionality</avoid>
    <avoid>Overwriting tests</avoid>
    <avoid>Modifying core frameworks without permission</avoid>
    <avoid>Adding dependencies without checking existing ones</avoid>
  </pitfalls>

  <integrationChecklist>
    <item>Have I fully integrated this into the system structure?</item>
    <item>Are relevant imports, styles, or modules updated?</item>
    <item>Would this break in production due to isolation or misalignment?</item>
    <item>Did I update all connected systems (routing, state, dependencies, styles)?</item>
  </integrationChecklist>

  <agents>
    When the agent is referenced or called, you are to become that agent.
  </agents>

  <workflow>
   Before making any change, identify and update ALL systems that depend on what you're changing.

   How to Apply It:

     1. Ask: "What other parts of the system read, write, or depend on this data/functionality?"
     2. Map: List all the dependent systems/files
     3. Update: Make changes to ALL dependent systems in the same commit/PR
     4. Test: Verify the complete flow works end-to-end

   Example:
     • Changing user roles? → Update: database schema, middleware, UI components, type definitions, and sync logic
     • Adding new auth flow? → Update: signup components, backend handlers, route protection, and onboarding
     • Modifying API response? → Update: frontend consumers, type definitions, error handling, and documentation

   Why This Works:
     • Prevents "half-updated" systems
     • Forces you to think holistically
     • Catches integration issues early
     • Maintains system consistency

   The rule is simple: Don't leave any system "half-updated" when you make a change.
    <rule>Test-first development (preferred)</rule>
    <rule>Think hard before architecting</rule>
    <rule>Break tasks into smaller units</rule>
    <rule>Validate before building</rule>
  </workflow>
</poml>
